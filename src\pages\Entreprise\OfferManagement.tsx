import React, { useState } from 'react';
import { FaPlus, FaChevronDown } from 'react-icons/fa';

// Données fictives pour les offres
const mockOffers = [
  {
    id: 1,
    logo: "🔴⚫", // Logo simplifié pour l'exemple
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    services: [
      { name: "Domiciliation", price: "3000DH" },
      { name: "Création", price: "3000DH" }
    ]
  },
  {
    id: 2,
    logo: "🔵🟡", // Logo AirJob simplifié
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    services: [
      { name: "Domiciliation", price: "2000DH" },
      { name: "Création", price: "2000DH" }
    ]
  },
  {
    id: 3,
    logo: "🟢⚪", // Logo AC simplifié
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    services: [
      { name: "Domiciliation", price: "2000DH" },
      { name: "Création", price: "2000DH" }
    ]
  },
  {
    id: 4,
    logo: "🔴⚫", // Logo simplifié pour l'exemple
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    services: [
      { name: "Domiciliation", price: "2000DH" },
      { name: "Création", price: "2000DH" }
    ]
  },
  {
    id: 5,
    logo: "🔵🟡", // Logo AirJob simplifié
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    services: [
      { name: "Domiciliation", price: "2000DH" },
      { name: "Création", price: "2000DH" }
    ]
  },
  {
    id: 6,
    logo: "🟢⚪", // Logo AC simplifié
    name: "Nom d'entreprise",
    ville: "Ville",
    address: "Adress",
    services: [
      { name: "Domiciliation", price: "2000DH" },
      { name: "Création", price: "2000DH" }
    ]
  }
];

const OfferManagement = () => {
  const [showCreateOfferModal, setShowCreateOfferModal] = useState(false);
  const [offerType, setOfferType] = useState("");

  // Composant pour une carte d'offre
  const OfferCard = ({ offer }: { offer: typeof mockOffers[0] }) => (
    <div className="bg-white rounded-xl shadow-sm overflow-hidden p-4">
      <div className="flex items-center mb-3">
        <div className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-100 mr-3 text-xl">
          {offer.logo}
        </div>
        <div>
          <p className="font-medium text-sm">{offer.name}</p>
          <p className="text-xs text-gray-500">{offer.address}</p>
        </div>
      </div>

      {/* Services */}
      <div className="space-y-2 mb-3">
        {offer.services.map((service, index) => (
          <div key={index} className="flex justify-between items-center">
            <span className="text-sm">{service.name}</span>
            <span className="font-bold text-sm">{service.price}</span>
          </div>
        ))}
      </div>

      <button className="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600 flex items-center justify-center">
        Voir plus <FaChevronDown className="ml-2" size={12} />
      </button>
    </div>
  );

  const handleCreateOffer = () => {
    setShowCreateOfferModal(true);
  };

  const handleCloseModal = () => {
    setShowCreateOfferModal(false);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-xl font-bold text-gray-800">Nos offres</h1>
        <button
          onClick={handleCreateOffer}
          className="bg-white text-blue-500 border border-blue-500 rounded-full p-2 flex items-center justify-center hover:bg-blue-50"
        >
          <FaPlus size={16} />
          <span className="ml-2 mr-1">Créer une offre</span>
        </button>
      </div>

      {/* Grid d'offres */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockOffers.map(offer => (
          <OfferCard key={offer.id} offer={offer} />
        ))}
      </div>

      {/* Modal pour créer une offre */}
      {showCreateOfferModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full md:w-[540px] p-6">
            <h2 className="text-xl font-semibold mb-4">Créer une nouvelle offre</h2>

            <form>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type d'offre
                </label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  value={offerType}
                  onChange={(e) => setOfferType(e.target.value)}
                >
                  <option value="">Sélectionnez un type</option>
                  <option value="Juridique">Juridique</option>
                  <option value="Domiciliation">Domiciliation</option>
                  <option value="Comptabilité">Comptabilité</option>
                </select>
              </div>

              {offerType === "Juridique" ? (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Type de service juridique
                  </label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Sélectionnez un service</option>
                    <option value="Création de société">Création de société</option>
                    <option value="modification-statuts">Modification des statuts</option>
                    <option value="transfert-siege">Transfert de siège social</option>
                    <option value="changement-dirigeant">Changement de dirigeant</option>
                    <option value="changement-denomination">Changement de dénomination sociale</option>
                    <option value="modification-objet">Modification de l'objet social</option>
                    <option value="augmentation-capital">Augmentation de capital</option>
                    <option value="reduction-capital">Réduction de capital</option>
                    <option value="cession-parts">Cession de parts sociales ou d'actions</option>
                    <option value="entree-sortie-associe">Entrée ou sortie d'un associé</option>
                  </select>
                </div>
              ) : (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Titre
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Offre domiciliation à bon prix"
                  />
                </div>
              )}

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Description de l'offre..."
                ></textarea>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prix
                </label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="2000"
                />
              </div>

              <div className="mb-6 flex items-center">
                <label htmlFor="conditions" className="flex items-center text-sm text-gray-700">
                  J'accepte les conditions
                  <input
                    type="checkbox"
                    id="conditions"
                    className="h-4 w-4 text-blue-500 focus:ring-blue-400 border-gray-300 rounded ml-2"
                  />
                </label>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  Créer
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default OfferManagement;