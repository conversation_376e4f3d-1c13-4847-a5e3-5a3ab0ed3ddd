import React, { useState } from 'react';
import { DomiciliationFormData } from './DomiciliationForm';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import './DatePickerStyles.css';
import { FaUser, FaBuilding, FaCalendarAlt } from 'react-icons/fa';

interface InformationStepProps {
  formData: DomiciliationFormData;
  updateFormData: (data: Partial<DomiciliationFormData>) => void;
  onNext: () => void;
}

const InformationStep: React.FC<InformationStepProps> = ({ formData, updateFormData, onNext }) => {
  const [startDate, setStartDate] = useState<Date | null>(formData.dateDebut ? new Date(formData.dateDebut) : null);
  const [endDate, setEndDate] = useState<Date | null>(formData.dateFin ? new Date(formData.dateFin) : null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });
  };

  const handleDateChange = (date: Date | null, type: 'start' | 'end') => {
    if (type === 'start') {
      setStartDate(date);
      updateFormData({ dateDebut: date ? date.toISOString() : '' });
    } else {
      setEndDate(date);
      updateFormData({ dateFin: date ? date.toISOString() : '' });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext();
  };

  const isFormValid = () => {
    return (
      formData.nomCompletGerant.trim() !== '' &&
      formData.cinGerant.trim() !== '' &&
      formData.adresseGerant.trim() !== '' &&
      formData.nomEntreprise.trim() !== '' &&
      formData.ice.trim() !== '' &&
      formData.dateDebut !== '' &&
      formData.dateFin !== ''
    );
  };

  return (
    <div className="py-4 px-2">
      <form onSubmit={handleSubmit}>
        {/* Informations du gérant */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-blue-600 mb-4 flex items-center">
            <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white rounded-full mr-2 text-sm">
              <FaUser />
            </span>
            Informations du gérant
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Nom complet du gérant */}
            <div>
              <label htmlFor="nomCompletGerant" className="block text-sm font-medium text-gray-700 mb-1">
                Nom complet du gérant
              </label>
              <input
                type="text"
                id="nomCompletGerant"
                name="nomCompletGerant"
                value={formData.nomCompletGerant}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm"
                placeholder="Nom complet"
                required
              />
            </div>

            {/* CIN du gérant */}
            <div>
              <label htmlFor="cinGerant" className="block text-sm font-medium text-gray-700 mb-1">
                CIN du gérant
              </label>
              <input
                type="text"
                id="cinGerant"
                name="cinGerant"
                value={formData.cinGerant}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm"
                placeholder="AB123456"
                required
              />
            </div>
          </div>

          {/* Adresse du Gérant */}
          <div className="mt-4">
            <label htmlFor="adresseGerant" className="block text-sm font-medium text-gray-700 mb-1">
              Adresse du gérant
            </label>
            <input
              type="text"
              id="adresseGerant"
              name="adresseGerant"
              value={formData.adresseGerant}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm"
              placeholder="Adresse complète"
              required
            />
          </div>
        </div>

        {/* Informations de l'entreprise */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-blue-600 mb-4 flex items-center">
            <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white rounded-full mr-2 text-sm">
              <FaBuilding />
            </span>
            Informations de l'entreprise
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Nom d'entreprise */}
            <div>
              <label htmlFor="nomEntreprise" className="block text-sm font-medium text-gray-700 mb-1">
                Nom d'entreprise
              </label>
              <input
                type="text"
                id="nomEntreprise"
                name="nomEntreprise"
                value={formData.nomEntreprise}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm"
                placeholder="Nom de l'entreprise"
                required
              />
            </div>

            {/* ICE */}
            <div>
              <label htmlFor="ice" className="block text-sm font-medium text-gray-700 mb-1">
                ICE
              </label>
              <input
                type="text"
                id="ice"
                name="ice"
                value={formData.ice}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm"
                placeholder="ABC12345"
                required
              />
            </div>
          </div>
        </div>

        {/* Durée de domiciliation */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-blue-600 mb-4 flex items-center">
            <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-600 text-white rounded-full mr-2 text-sm">
              <FaCalendarAlt />
            </span>
            Durée de domiciliation
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Date de début */}
            <div>
              <label htmlFor="dateDebut" className="block text-sm font-medium text-gray-700 mb-1">
                Date de début
              </label>
              <DatePicker
                selected={startDate}
                onChange={(date) => handleDateChange(date, 'start')}
                selectsStart
                startDate={startDate}
                endDate={endDate}
                dateFormat="dd/MM/yyyy"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm"
                placeholderText="Sélectionner une date"
                required
                wrapperClassName="w-full"
              />
            </div>

            {/* Date de fin */}
            <div>
              <label htmlFor="dateFin" className="block text-sm font-medium text-gray-700 mb-1">
                Date de fin
              </label>
              <DatePicker
                selected={endDate}
                onChange={(date) => handleDateChange(date, 'end')}
                selectsEnd
                startDate={startDate}
                endDate={endDate}
                minDate={startDate || undefined}
                dateFormat="dd/MM/yyyy"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 shadow-sm"
                placeholderText="Sélectionner une date"
                required
                wrapperClassName="w-full"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <button
            type="submit"
            disabled={!isFormValid()}
            className={`px-10 py-2 rounded-full text-white font-medium shadow-md ${
              isFormValid() ? 'bg-gray-400 hover:bg-gray-500' : 'bg-gray-300 cursor-not-allowed'
            }`}
          >
            Soumettre
          </button>
        </div>
      </form>
    </div>
  );
};

export default InformationStep;
