import { useState } from "react";
import { MdDashboard, MdLocalOffer, MdNotifications, Md<PERSON>erson, MdTrendingUp, MdDescription } from "react-icons/md";
import NavBar from "../../components/NavBar";
import EntrepriseSidebar from "../../components/Entreprise/EntrepriseSidebar";
import { useAuthContext } from "../../contexts/AuthContext";

// Import all pages that will be displayed in the dashboard
import DomiciliationManagement from "./DomiciliationManagement";
import DashboardManagement from "./DashboardManagement";
import CreationManagement from "./CreationManagement";
import AnnoncesManagement from "./AnnoncesManagement";
import ComptabiliteManagement from "./ComptabiliteManagement";
import OfferManagement from "./OfferManagement";
import EntrepriseSupportPage from "./SupportPage";
import EntrepriseProfilePage from "./ProfilePage";
import DocumentsListPage from "./DocumentsListPage";

// Define the sections of the sidebar
const sections = [
        { icon: MdDashboard, label: "Dashboard", link: "/entreprise/dashboardmanagement" },
        { icon: Md<PERSON><PERSON>, label: "Profil", link: "/entreprise/profile" },
        { icon: MdPerson, label: "Juridique", link: "/entreprise/creationmanagement" },
        { icon: MdNotifications, label: "Domicialisation", link: "/entreprise/domicialisationmanagement" },
        { icon: MdTrendingUp, label: "Annonces Légales", link: "/entreprise/annoncesmanagement" },
        { icon: MdLocalOffer, label: "Comptabilite", link: "/entreprise/comptabilitemanagement" },
        { icon: MdLocalOffer, label: "Gestion des offres", link: "/entreprise/offermanagement" },
        { icon: MdDescription, label: "Documents", link: "/entreprise/documents" },
        { icon: MdLocalOffer, label: "Support", link: "/entreprise/support" },
];

export const DashboardEntreprisePage = () => {
        const [currentPage, setCurrentPage] = useState("/entreprise/dashboardmanagement");
        const [isLoading, setIsLoading] = useState(false);
        const { user } = useAuthContext();

        const handleLinkClick = (link: string) => {
                if (link !== currentPage) {
                        setIsLoading(true);
                        setCurrentPage(link);
                        // Simuler un temps de chargement court
                        setTimeout(() => {
                                setIsLoading(false);
                        }, 300);
                }
        };

        // Fonction pour rendre le composant approprié en fonction de la page actuelle
        const renderCurrentPage = () => {
                switch (currentPage) {
                        case "/entreprise/dashboardmanagement":
                                return <DashboardManagement />;
                        case "/entreprise/profile":
                                return <EntrepriseProfilePage />;
                        case "/entreprise/creationmanagement":
                                return <CreationManagement />;
                        case "/entreprise/domicialisationmanagement":
                                return <DomiciliationManagement />;
                        case "/entreprise/annoncesmanagement":
                                return <AnnoncesManagement />;
                        case "/entreprise/comptabilitemanagement":
                                return <ComptabiliteManagement />;
                        case "/entreprise/offermanagement":
                                return <OfferManagement />;
                        case "/entreprise/offres":
                                return <OfferManagement />;
                        case "/entreprise/documents":
                                return <DocumentsListPage />;
                        case "/entreprise/support":
                                return <EntrepriseSupportPage />;
                        default:
                                return <DashboardManagement />;
                }
        };

        return (
                <div className="flex flex-col min-h-screen">
                        <NavBar
                                title="Dashboard"
                                userName={user?.name}
                                userImage={user?.profileImage}
                                onProfileClick={handleLinkClick}
                        />
                        <div className="flex flex-1 overflow-auto">
                                {/* Sidebar */}
                                <EntrepriseSidebar
                                        sections={sections}
                                        onLinkClick={handleLinkClick}
                                />

                                {/* Main Content */}
                                <div className="flex-1 p-4 overflow-auto">
                                        {isLoading ? (
                                                <div className="w-full h-full flex items-center justify-center">
                                                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                                                </div>
                                        ) : (
                                                <div className="w-full bg-white rounded-lg shadow-md p-4">
                                                        {renderCurrentPage()}
                                                </div>
                                        )}
                                </div>
                        </div>
                </div>
        );
}
