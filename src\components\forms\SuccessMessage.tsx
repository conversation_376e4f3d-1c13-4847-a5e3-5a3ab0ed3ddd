import React from 'react';
import { FaCheckCircle, FaArrowLeft } from 'react-icons/fa';

interface SuccessMessageProps {
  onClose?: () => void;
  title?: string;
  message?: string;
}

const SuccessMessage: React.FC<SuccessMessageProps> = ({ 
  onClose, 
  title = "Demande enregistrée avec succès",
  message = "La demande de création de votre entreprise a bien été enregistrée. Nous attendons maintenant la validation de votre nomination par l'OMPIC, ce qui peut prendre de 24 à 48 heures."
}) => {
  return (
    <div className="bg-white p-6">
      <div className="max-w-2xl mx-auto">
        {/* Header avec bouton retour */}
        <div className="mb-8">
          {onClose && (
            <button
              onClick={onClose}
              className="text-blue-600 hover:text-blue-700 mb-4 p-2 rounded-full hover:bg-blue-50 transition-colors flex items-center"
            >
              <FaArrowLeft size={16} className="mr-2" />
              Retour au dashboard
            </button>
          )}
        </div>

        {/* Contenu principal du message de succès */}
        <div className="text-center">
          {/* Icône de validation */}
          <div className="mb-6">
            <div className="mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
              <FaCheckCircle className="w-12 h-12 text-green-500" />
            </div>
          </div>

          {/* Titre */}
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {title}
          </h2>

          {/* Message principal */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <p className="text-gray-700 leading-relaxed">
              {message}
            </p>
          </div>

          {/* Informations supplémentaires */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">
              Prochaines étapes
            </h3>
            <div className="text-left space-y-3">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  1
                </div>
                <p className="text-blue-700">
                  Validation de votre nomination par l'OMPIC (24-48h)
                </p>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  2
                </div>
                <p className="text-blue-700">
                  Notification par email une fois la validation terminée
                </p>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">
                  3
                </div>
                <p className="text-blue-700">
                  Finalisation de votre dossier de création d'entreprise
                </p>
              </div>
            </div>
          </div>

          {/* Informations de contact */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">
              Besoin d'aide ?
            </h3>
            <p className="text-gray-600 mb-3">
              Notre équipe est disponible pour répondre à vos questions
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <div className="text-center">
                <p className="text-sm text-gray-500">Email</p>
                <p className="font-medium text-blue-600"><EMAIL></p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500">Téléphone</p>
                <p className="font-medium text-blue-600">+212 5XX XX XX XX</p>
              </div>
            </div>
          </div>

          {/* Boutons d'action */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {onClose && (
              <button
                onClick={onClose}
                className="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 font-medium transition-colors"
              >
                Retour au dashboard
              </button>
            )}
            <button
              onClick={() => window.print()}
              className="px-8 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 font-medium transition-colors"
            >
              Imprimer la confirmation
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessMessage;
