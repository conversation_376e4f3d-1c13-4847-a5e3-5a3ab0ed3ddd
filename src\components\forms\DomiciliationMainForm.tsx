import React, { useState } from 'react';
import { FaUser, FaBuilding, FaCalendarAlt, FaArrowLeft } from 'react-icons/fa';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import SuccessMessage from './SuccessMessage';

interface DomiciliationMainFormProps {
  onClose?: () => void;
}

export interface DomiciliationFormData {
  // Informations du gérant
  nomCompletGerant: string;
  cinGerant: string;
  adresseGerant: string;
  // Informations entreprise
  nomEntreprise: string;
  ice: string;
  // Durée de domiciliation
  dateDebut: string;
  dateFin: string;
}

const DomiciliationMainForm: React.FC<DomiciliationMainFormProps> = ({ onClose }) => {
  const [showSuccess, setShowSuccess] = useState(false);
  const [formData, setFormData] = useState<DomiciliationFormData>({
    nomCompletGerant: '',
    cinGerant: '',
    adresseGerant: '',
    nomEntreprise: '',
    ice: '',
    dateDebut: '',
    dateFin: '',
  });

  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (date: Date | null, type: 'start' | 'end') => {
    if (type === 'start') {
      setStartDate(date);
      setFormData(prev => ({ ...prev, dateDebut: date ? date.toISOString() : '' }));
    } else {
      setEndDate(date);
      setFormData(prev => ({ ...prev, dateFin: date ? date.toISOString() : '' }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Formulaire de domiciliation soumis avec les données:', formData);
    // Afficher le message de succès au lieu de fermer directement
    setShowSuccess(true);
  };

  const isFormValid = () => {
    return (
      formData.nomCompletGerant.trim() !== '' &&
      formData.cinGerant.trim() !== '' &&
      formData.adresseGerant.trim() !== '' &&
      formData.nomEntreprise.trim() !== '' &&
      formData.ice.trim() !== '' &&
      formData.dateDebut !== '' &&
      formData.dateFin !== ''
    );
  };

  // Si le message de succès doit être affiché
  if (showSuccess) {
    return (
      <SuccessMessage
        onClose={onClose}
        title="Domiciliation créée avec succès"
        message="Votre demande de domiciliation a bien été enregistrée. Nous procéderons à la validation de votre dossier dans les plus brefs délais. Vous recevrez une confirmation par email une fois le processus terminé."
      />
    );
  }

  return (
    <div className="bg-white p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            {onClose && (
              <button
                onClick={onClose}
                className="text-blue-600 hover:text-blue-700 mr-4 p-2 rounded-full hover:bg-blue-50 transition-colors"
              >
                <FaArrowLeft size={20} />
              </button>
            )}
            <h2 className="text-2xl font-bold text-gray-900">Création de domiciliation</h2>
          </div>
          <p className="text-gray-600">Remplissez les informations nécessaires pour créer une domiciliation</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Informations du gérant */}
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <h3 className="text-xl font-semibold text-blue-600 mb-6 flex items-center">
              <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full mr-3">
                <FaUser />
              </span>
              Informations du gérant
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Nom complet du gérant */}
              <div>
                <label htmlFor="nomCompletGerant" className="block text-sm font-medium text-gray-700 mb-2">
                  Nom complet du gérant
                </label>
                <input
                  type="text"
                  id="nomCompletGerant"
                  name="nomCompletGerant"
                  value={formData.nomCompletGerant}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
                  placeholder="Nom complet"
                  required
                />
              </div>

              {/* CIN du gérant */}
              <div>
                <label htmlFor="cinGerant" className="block text-sm font-medium text-gray-700 mb-2">
                  CIN du gérant
                </label>
                <input
                  type="text"
                  id="cinGerant"
                  name="cinGerant"
                  value={formData.cinGerant}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
                  placeholder="AB123456"
                  required
                />
              </div>
            </div>

            {/* Adresse du Gérant */}
            <div className="mt-6">
              <label htmlFor="adresseGerant" className="block text-sm font-medium text-gray-700 mb-2">
                Adresse du gérant
              </label>
              <input
                type="text"
                id="adresseGerant"
                name="adresseGerant"
                value={formData.adresseGerant}
                onChange={handleChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
                placeholder="Adresse complète"
                required
              />
            </div>
          </div>

          {/* Informations de l'entreprise */}
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <h3 className="text-xl font-semibold text-blue-600 mb-6 flex items-center">
              <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full mr-3">
                <FaBuilding />
              </span>
              Informations de l'entreprise
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Nom d'entreprise */}
              <div>
                <label htmlFor="nomEntreprise" className="block text-sm font-medium text-gray-700 mb-2">
                  Nom d'entreprise
                </label>
                <input
                  type="text"
                  id="nomEntreprise"
                  name="nomEntreprise"
                  value={formData.nomEntreprise}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
                  placeholder="Nom de l'entreprise"
                  required
                />
              </div>

              {/* ICE */}
              <div>
                <label htmlFor="ice" className="block text-sm font-medium text-gray-700 mb-2">
                  ICE
                </label>
                <input
                  type="text"
                  id="ice"
                  name="ice"
                  value={formData.ice}
                  onChange={handleChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
                  placeholder="ABC12345"
                  required
                />
              </div>
            </div>
          </div>

          {/* Durée de domiciliation */}
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <h3 className="text-xl font-semibold text-blue-600 mb-6 flex items-center">
              <span className="inline-flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full mr-3">
                <FaCalendarAlt />
              </span>
              Durée de domiciliation
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Date de début */}
              <div>
                <label htmlFor="dateDebut" className="block text-sm font-medium text-gray-700 mb-2">
                  Date de début
                </label>
                <DatePicker
                  selected={startDate}
                  onChange={(date) => handleDateChange(date, 'start')}
                  selectsStart
                  startDate={startDate}
                  endDate={endDate}
                  dateFormat="dd/MM/yyyy"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
                  placeholderText="Sélectionner une date"
                  required
                  wrapperClassName="w-full"
                />
              </div>

              {/* Date de fin */}
              <div>
                <label htmlFor="dateFin" className="block text-sm font-medium text-gray-700 mb-2">
                  Date de fin
                </label>
                <DatePicker
                  selected={endDate}
                  onChange={(date) => handleDateChange(date, 'end')}
                  selectsEnd
                  startDate={startDate}
                  endDate={endDate}
                  minDate={startDate || undefined}
                  dateFormat="dd/MM/yyyy"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
                  placeholderText="Sélectionner une date"
                  required
                  wrapperClassName="w-full"
                />
              </div>
            </div>
          </div>

          {/* Boutons d'action */}
          <div className="flex justify-between items-center pt-6">
            {onClose && (
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 font-medium transition-colors"
              >
                Annuler
              </button>
            )}

            <button
              type="submit"
              disabled={!isFormValid()}
              className={`px-8 py-3 rounded-lg text-white font-medium transition-colors ${
                isFormValid()
                  ? 'bg-blue-500 hover:bg-blue-600'
                  : 'bg-gray-300 cursor-not-allowed'
              }`}
            >
              Créer la domiciliation
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DomiciliationMainForm;
