import React, { useState } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaPlus, FaEye, FaEdit, FaTrash } from 'react-icons/fa';

// Mock data for documents
const mockDocuments = [
  { id: 1, name: 'Certificat Negative' },
  { id: 2, name: 'Dossier Juridiques' },
  { id: 3, name: 'Enregistrement Dossier' },
  { id: 4, name: '<PERSON><PERSON>' },
  { id: 5, name: 'Registe de commerce' },
  { id: 6, name: 'RC Model 7' },
  { id: 7, name: 'Identifiant Fiscale' },
  { id: 8, name: 'Code Sample' },
  { id: 9, name: '<PERSON><PERSON><PERSON> CNSS' },
  { id: 10, name: 'Attestation CNSS' },
];

const DocumentsListPage = () => {
  const [selectedDocuments, setSelectedDocuments] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // <PERSON>le select all checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedDocuments([]);
    } else {
      setSelectedDocuments(mockDocuments.map(doc => doc.id));
    }
    setSelectAll(!selectAll);
  };

  // Handle individual document selection
  const handleSelectDocument = (id: number) => {
    if (selectedDocuments.includes(id)) {
      setSelectedDocuments(selectedDocuments.filter(docId => docId !== id));
      setSelectAll(false);
    } else {
      setSelectedDocuments([...selectedDocuments, id]);
      if (selectedDocuments.length + 1 === mockDocuments.length) {
        setSelectAll(true);
      }
    }
  };

  // Handle add new document
  const handleAddDocument = () => {
    // Implement add document functionality
    console.log('Add new document');
  };

  // Handle edit document
  const handleEditDocument = (id: number) => {
    // Implement edit document functionality
    console.log('Edit document with ID:', id);
  };

  // Handle view document
  const handleViewDocument = (id: number) => {
    // Implement view document functionality
    console.log('View document with ID:', id);
  };

  // Handle delete document
  const handleDeleteDocument = (id: number) => {
    // Implement delete document functionality
    console.log('Delete document with ID:', id);
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Liste des Documents</h1>
        <div className="flex space-x-2">
          <button
            className="bg-gray-200 text-gray-700 rounded-full p-2 hover:bg-gray-300 transition-colors"
            title="Filtrer"
          >
            <FaFilter className="w-5 h-5" />
          </button>
          <button
            onClick={handleAddDocument}
            className="bg-blue-500 text-white rounded-full p-2 hover:bg-blue-600 transition-colors"
            title="Ajouter un document"
          >
            <FaPlus className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Documents Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Document
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {mockDocuments.map((document) => (
              <tr key={document.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedDocuments.includes(document.id)}
                      onChange={() => handleSelectDocument(document.id)}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{document.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => handleDeleteDocument(document.id)}
                      className="text-red-500 hover:text-red-700 transition-colors"
                      title="Supprimer"
                    >
                      <FaTrash className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleEditDocument(document.id)}
                      className="text-green-500 hover:text-green-700 transition-colors"
                      title="Éditer"
                    >
                      <FaEdit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleAddDocument()}
                      className="text-blue-500 hover:text-blue-700 transition-colors"
                      title="Ajouter"
                    >
                      <FaPlus className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="mt-4 flex justify-center">
        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <a
            href="#"
            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
          >
            <span className="sr-only">Previous</span>
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </a>
          <a
            href="#"
            aria-current="page"
            className="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium"
          >
            1
          </a>
          <a
            href="#"
            className="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium"
          >
            2
          </a>
          <a
            href="#"
            className="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium"
          >
            3
          </a>
          <a
            href="#"
            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
          >
            <span className="sr-only">Next</span>
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          </a>
        </nav>
      </div>
    </div>
  );
};

export default DocumentsListPage;
