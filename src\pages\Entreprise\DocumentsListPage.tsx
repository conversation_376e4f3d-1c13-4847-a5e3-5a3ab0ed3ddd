import React, { useState } from 'react';
import { FaEdit, FaTrash } from 'react-icons/fa';

// Mock data for documents
const mockDocuments = [
  { id: 1, name: 'Certificat Negative' },
  { id: 2, name: 'Dossier Juridiques' },
  { id: 3, name: 'Enregistrement Dossier' },
  { id: 4, name: '<PERSON>e' },
  { id: 5, name: 'Registe de commerce' },
  { id: 6, name: 'RC Model 7' },
  { id: 7, name: 'Identifiant Fiscale' },
  { id: 8, name: 'Code Sample' },
  { id: 9, name: 'Demande CNSS' },
  { id: 10, name: 'Attestation CNSS' },
];

interface DocumentsListPageProps {
  orderId?: number;
  orderReference?: string;
  onClose?: () => void;
}

const DocumentsListPage: React.FC<DocumentsListPageProps> = ({
  orderId,
  orderReference,
  onClose
}) => {
  const [selectedDocuments, setSelectedDocuments] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedDocuments([]);
    } else {
      setSelectedDocuments(mockDocuments.map(doc => doc.id));
    }
    setSelectAll(!selectAll);
  };

  // Handle individual document selection
  const handleSelectDocument = (id: number) => {
    if (selectedDocuments.includes(id)) {
      setSelectedDocuments(selectedDocuments.filter(docId => docId !== id));
      setSelectAll(false);
    } else {
      setSelectedDocuments([...selectedDocuments, id]);
      if (selectedDocuments.length + 1 === mockDocuments.length) {
        setSelectAll(true);
      }
    }
  };

  // Handle add new document
  const handleAddDocument = () => {
    // Implement add document functionality
    console.log('Add new document');
  };

  // Handle edit document
  const handleEditDocument = (id: number) => {
    // Implement edit document functionality
    console.log('Edit document with ID:', id);
  };

  // Handle view document
  const handleViewDocument = (id: number) => {
    // Implement view document functionality
    console.log('View document with ID:', id);
  };

  // Handle delete document
  const handleDeleteDocument = (id: number) => {
    // Implement delete document functionality
    console.log('Delete document with ID:', id);
  };

  return (
    <div className="bg-white p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Documents Juridiques</h2>
          {orderReference && (
            <p className="text-gray-600">
              Dossier: {orderReference} {orderId && `(ID: ${orderId})`}
            </p>
          )}
        </div>

        {/* Documents List */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-xl font-semibold text-blue-600 mb-6">Liste des Documents</h3>

          <div className="space-y-4">
            {mockDocuments.map((document) => (
              <div key={document.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <input
                    type="checkbox"
                    checked={selectedDocuments.includes(document.id)}
                    onChange={() => handleSelectDocument(document.id)}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 text-sm font-medium">📄</span>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{document.name}</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEditDocument(document.id)}
                    className="text-green-500 hover:text-green-700 transition-colors p-2 rounded-full hover:bg-green-50"
                    title="Éditer"
                  >
                    <FaEdit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteDocument(document.id)}
                    className="text-red-500 hover:text-red-700 transition-colors p-2 rounded-full hover:bg-red-50"
                    title="Supprimer"
                  >
                    <FaTrash className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Select All Option */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <label className="flex items-center space-x-3 text-sm text-gray-700">
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span>Sélectionner tous les documents</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentsListPage;
