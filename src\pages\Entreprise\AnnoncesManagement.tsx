import React, { useState } from 'react';
import { Fa<PERSON><PERSON><PERSON>, FaPlus } from 'react-icons/fa';
import { IoMdArrowBack, IoMdArrowForward } from 'react-icons/io';
import DomiciliationMainForm from '../../components/forms/DomiciliationMainForm';

// Mock data for the page
const mockData = {
  totalNet: 12750,
  commandesBloquees: 5600,
  commandesTotales: 3460,
  transactions: [
    { id: 1, nom: 'Nom de facture', date: '28 January 2024', montant: 5400, type: 'credit' },
    { id: 2, nom: 'Nom de facture', date: '25 January 2024', montant: 850, type: 'debit' },
    { id: 3, nom: 'Nom de facture', date: '21 January 2024', montant: 5400, type: 'credit' },
    { id: 4, nom: 'Nom de facture', date: '21 January 2024', montant: 5400, type: 'credit' },
  ],
  ordres: [
    { id: 1, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', gerant: 'Shopping', telephone: 'Juridique entreprise', enregistrement: '28 Jan, 12:30 AM', situation: 'Complet' },
    { id: 2, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', gerant: 'Transfer', telephone: 'Domicialisation', enregistrement: '25 Jan, 10:40 PM', situation: 'Etap 2' },
    { id: 3, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', gerant: 'Service', telephone: 'Accompagnement', enregistrement: '20 Jan, 10:40 PM', situation: 'Etap 1' },
    { id: 4, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', gerant: 'Transfer', telephone: 'Accompagnement', enregistrement: '15 Jan, 03:29 PM', situation: 'Complet' },
  ],
  domiciliations: [
    { id: 1, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', gerant: 'Shopping', telephone: 'Juridique entreprise', enregistrement: '28 Jan, 12:30 AM', echeance: '28 Jan, 12:30 AM', situation: 'Complet' },
    { id: 2, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', gerant: 'Transfer', telephone: 'Domicialisation', enregistrement: '25 Jan, 10:40 PM', echeance: '28 Jan, 12:30 AM', situation: 'Etap 2' },
    { id: 3, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', gerant: 'Service', telephone: 'Accompagnement', enregistrement: '20 Jan, 10:40 PM', echeance: '28 Jan, 12:30 AM', situation: 'Etap 1' },
    { id: 4, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', gerant: 'Transfer', telephone: 'Accompagnement', enregistrement: '15 Jan, 03:29 PM', echeance: '28 Jan, 12:30 AM', situation: 'Complet' },
  ]
};

// Activity data for the chart
const activityData = {
  domicialisation1: [220, 120, 240, 350, 230, 220, 320],
  domicialisation2: [450, 330, 310, 450, 140, 380, 380],
  days: ['Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri']
};

const AnnoncesManagement = () => {
  const [showDomiciliationForm, setShowDomiciliationForm] = useState(false);

  const handleOpenDomiciliationForm = () => {
    setShowDomiciliationForm(true);
  };

  const handleCloseDomiciliationForm = () => {
    setShowDomiciliationForm(false);
  };

  // Si le formulaire de domiciliation est affiché, on affiche seulement le formulaire
  if (showDomiciliationForm) {
    return (
      <div className="bg-blue-100 min-h-screen">
        <DomiciliationMainForm onClose={handleCloseDomiciliationForm} />
      </div>
    );
  }

  return (
    <div>

      {/* Top Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {/* Total Net Card */}
        <div className="bg-white rounded-lg shadow p-4 flex items-center">
          <div className="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center mr-4">
            <svg className="w-6 h-6 text-teal-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-gray-500 text-sm">Total net</p>
            <p className="text-2xl font-bold">${mockData.totalNet}</p>
          </div>
        </div>

        {/* Commandes Bloquées Card */}
        <div className="bg-white rounded-lg shadow p-4 flex items-center">
          <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
            <svg className="w-6 h-6 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-gray-500 text-sm">Commandes bloquées</p>
            <p className="text-2xl font-bold">${mockData.commandesBloquees}</p>
          </div>
        </div>

        {/* Commandes Totales Card */}
        <div className="bg-white rounded-lg shadow p-4 flex items-center">
          <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center mr-4">
            <svg className="w-6 h-6 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-gray-500 text-sm">Commandes totales</p>
            <p className="text-2xl font-bold">{mockData.commandesTotales}</p>
          </div>
        </div>

        {/* Create Domiciliation Button */}
        <div className="bg-white rounded-lg shadow p-4 flex flex-col justify-between">
          <button
            onClick={handleOpenDomiciliationForm}
            className="mt-2 bg-orange-500 text-white rounded-full py-2 px-4 flex items-center justify-center hover:bg-orange-600 transition-colors"
          >
            <FaPlus className="mr-2" /> Créer une domiciliation
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column - Activities Chart */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-4">Activités</h2>

          {/* Chart Legend */}
          <div className="flex items-center mb-4 space-x-4">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-teal-400 mr-2"></div>
              <span className="text-sm text-gray-600">domicialisation 1</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
              <span className="text-sm text-gray-600">domicialisation 2</span>
            </div>
          </div>

          {/* Simple Bar Chart */}
          <div className="flex h-64 items-end space-x-2">
            {activityData.days.map((day, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div className="w-full flex justify-center space-x-2">
                  <div
                    className="w-4 bg-blue-500 rounded-t-sm"
                    style={{ height: `${activityData.domicialisation2[index]}px`, maxHeight: '200px' }}
                  ></div>
                  <div
                    className="w-4 bg-teal-400 rounded-t-sm"
                    style={{ height: `${activityData.domicialisation1[index]}px`, maxHeight: '200px' }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-2">{day}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Column - Recent Transactions */}
        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-lg font-semibold mb-4">Transactions récentes</h2>
          <div className="space-y-4">
            {mockData.transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-2 border-b">
                <div className="flex items-center">
                  <div className={`w-10 h-10 rounded-full ${transaction.type === 'credit' ? 'bg-teal-100' : 'bg-red-100'} flex items-center justify-center mr-3`}>
                    <svg className={`w-5 h-5 ${transaction.type === 'credit' ? 'text-teal-500' : 'text-red-500'}`} fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium">{transaction.nom}</p>
                    <p className="text-sm text-gray-500">{transaction.date}</p>
                  </div>
                </div>
                <div className={`font-semibold ${transaction.type === 'credit' ? 'text-green-500' : 'text-red-500'}`}>
                  {transaction.type === 'credit' ? '+' : '-'}${transaction.montant}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="mt-6 bg-white rounded-lg shadow">
        <h2 className="text-lg font-semibold p-4 border-b">Ordres</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nomination</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gerant</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Telephone</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enregistrement</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Situation</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mockData.ordres.map((ordre) => (
                <tr key={ordre.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-3 w-3 rounded-full bg-teal-400 mr-2"></div>
                      <div className="text-sm font-medium text-gray-900">{ordre.reference}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.nomination}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.gerant}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.telephone}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.enregistrement}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${ordre.situation === 'Complet' ? 'bg-green-100 text-green-800' :
                        ordre.situation === 'Etap 2' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'}`}>
                      {ordre.situation}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                <button className="px-2 py-1 text-blue-500 hover:text-blue-700">
                  <IoMdArrowBack />
                </button>
                <span className="font-medium mx-1 px-3 py-1 bg-blue-500 text-white rounded-md">1</span>
                <span className="font-medium mx-1 px-3 py-1 hover:bg-gray-200 rounded-md">2</span>
                <span className="font-medium mx-1 px-3 py-1 hover:bg-gray-200 rounded-md">3</span>
                <span className="font-medium mx-1 px-3 py-1 hover:bg-gray-200 rounded-md">4</span>
                <button className="px-2 py-1 text-blue-500 hover:text-blue-700">
                  <IoMdArrowForward />
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnoncesManagement;
