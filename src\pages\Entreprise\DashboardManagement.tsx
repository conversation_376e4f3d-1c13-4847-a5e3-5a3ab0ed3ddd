import { useState } from 'react';
import { FaMapMarkerAlt } from 'react-icons/fa';
import { IoMdArrowBack, IoMdArrowForward } from 'react-icons/io';
import { MdOutlineAttachMoney } from 'react-icons/md';
import DomiciliationForm from '../../components/domiciliation/DomiciliationForm';

// Mock data for the page
const mockData = {
  totalNet: 12750,
  commandesBloquees: 5600,
  commandesTotales: 3460,
  transactions: [
    { id: 1, nom: 'Nom de facture', date: '25 Aug 2024', montant: 150, type: 'debit', status: 'Pending' },
    { id: 2, nom: 'Nom de facture', date: '25 Aug 2024', montant: 340, type: 'debit', status: 'Completed' },
    { id: 3, nom: 'Nom de facture', date: '25 Aug 2021', montant: 780, type: 'credit', status: 'Completed' },
  ],
  ordres: [
    { id: 1, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', nom: 'Shopping', service: 'Juridique entreprise', date: '28 Jan, 12:30 AM', situation: 'Complet', prix: '1200 DH' },
    { id: 2, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', nom: 'Transfer', service: 'Domicialisation', date: '25 Jan, 10:40 PM', situation: 'Etap 2', prix: '700DH' },
    { id: 3, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', nom: 'Service', service: 'Accompagnement', date: '20 Jan, 10:40 PM', situation: 'Etap 1', prix: '1000DH' },
    { id: 4, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', nom: 'Transfer', service: 'Accompagnement', date: '15 Jan, 03:29 PM', situation: 'Complet', prix: '1100DH' },
    { id: 5, reference: '123 Ain Sbaa, Casablanca', nomination: '#12548796', nom: 'Transfer', service: 'Domicialisation', date: '14 Jan, 10:40 PM', situation: 'Complet', prix: 'Complet' },
  ]
};

// Card expense statistics data
const cardExpenseStats = [
  { category: 'Creation', percentage: 25, color: '#4F7DF3' },
  { category: 'Comptabilité', percentage: 30, color: '#01D0B6' },
  { category: 'Domicialisation', percentage: 35, color: '#FF6384' },
  { category: 'Annonce', percentage: 10, color: '#FFCD56' }
];

const DashboardManagement = () => {
  const [showDomiciliationForm, setShowDomiciliationForm] = useState(false);

  const handleCloseDomiciliationForm = () => {
    setShowDomiciliationForm(false);
  };

  return (
    <div className="bg-white p-6">
      {/* Formulaire de domiciliation */}
      {showDomiciliationForm && (
        <DomiciliationForm onClose={handleCloseDomiciliationForm} />
      )}

      {/* Top Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Total Net Card */}
        <div className="bg-white rounded-lg shadow-md p-5 flex items-center">
          <div className="w-12 h-12 rounded-full bg-teal-100 flex items-center justify-center mr-4">
            <MdOutlineAttachMoney className="w-6 h-6 text-teal-500" />
          </div>
          <div>
            <p className="text-gray-500 text-sm">Total net</p>
            <p className="text-2xl font-bold">${mockData.totalNet}</p>
          </div>
        </div>

        {/* Commandes Bloquées Card */}
        <div className="bg-white rounded-lg shadow-md p-5 flex items-center">
          <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mr-4">
            <svg className="w-6 h-6 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-gray-500 text-sm">Commandes bloquées</p>
            <p className="text-2xl font-bold">${mockData.commandesBloquees}</p>
          </div>
        </div>

        {/* Commandes Totales Card */}
        <div className="bg-white rounded-lg shadow-md p-5 flex items-center">
          <div className="w-12 h-12 rounded-full bg-pink-100 flex items-center justify-center mr-4">
            <svg className="w-6 h-6 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-gray-500 text-sm">Commandes totales</p>
            <p className="text-2xl font-bold">${mockData.commandesTotales}</p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-5 px-2">
          <h2 className="text-xl font-semibold">Transactions</h2>
          <div className="text-blue-500 font-medium">Card Expense Statistics</div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Left Column - Transactions */}
          <div className="bg-white rounded-lg shadow-md p-5">
            {mockData.transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between py-4 px-2 border-b">
                <div className="flex items-center">
                  <div className="mr-4">
                    <FaMapMarkerAlt className="text-red-500" />
                  </div>
                  <div>
                    <p className="font-medium">{transaction.nom}</p>
                    <p className="text-sm text-gray-500">{transaction.date}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="mr-8 text-gray-500">
                    Type
                  </div>
                  <div className="mr-8 text-gray-500">
                    1234 ****
                  </div>
                  <div className="mr-8 text-blue-500">
                    {transaction.status}
                  </div>
                  <div className="text-red-500 font-medium">
                    {transaction.type === 'credit' ? '+' : '-'}${transaction.montant}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Right Column - Card Expense Statistics */}
          <div className="bg-white rounded-lg shadow-md p-5 flex flex-col items-center justify-center">
            <div className="relative w-56 h-56">
              {/* Donut Chart */}
              <svg viewBox="0 0 100 100" className="w-full h-full">
                <circle cx="50" cy="50" r="40" fill="none" stroke="#f5f5f5" strokeWidth="15" />

                {/* Calculate stroke-dasharray and stroke-dashoffset for each segment */}
                {cardExpenseStats.map((stat, index) => {
                  // Calculate the starting position based on previous segments
                  const previousPercentage = cardExpenseStats
                    .slice(0, index)
                    .reduce((acc, curr) => acc + curr.percentage, 0);

                  // Calculate the circumference
                  const circumference = 2 * Math.PI * 40;

                  // Calculate the dash array (length of the colored segment)
                  const dashArray = (stat.percentage / 100) * circumference;

                  // Calculate the dash offset (starting position)
                  const dashOffset = ((100 - previousPercentage - stat.percentage) / 100) * circumference;

                  return (
                    <circle
                      key={index}
                      cx="50"
                      cy="50"
                      r="40"
                      fill="none"
                      stroke={stat.color}
                      strokeWidth="15"
                      strokeDasharray={`${dashArray} ${circumference - dashArray}`}
                      strokeDashoffset={dashOffset}
                      transform="rotate(-90 50 50)"
                    />
                  );
                })}
              </svg>
            </div>

            {/* Legend - Centered below the chart */}
            <div className="flex flex-wrap justify-center mt-4 gap-4">
              {cardExpenseStats.map((stat, index) => (
                <div key={index} className="flex items-center">
                  <div style={{ backgroundColor: stat.color }} className="w-3 h-3 rounded-full mr-2"></div>
                  <span className="text-sm text-gray-600">{stat.category}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="p-5 border-b">
          <h2 className="text-xl font-semibold">Ordres</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">N° Dossier</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Situation</th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prix</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mockData.ordres.map((ordre) => (
                <tr key={ordre.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.reference}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.nomination}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.nom}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.service}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.date}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${ordre.situation === 'Complet' ? 'bg-green-100 text-green-800' :
                        ordre.situation === 'Etap 2' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'}`}>
                      {ordre.situation}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{ordre.prix}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination en bas à gauche */}
        <div className="p-5 border-t flex justify-start">
          <div className="flex items-center">
            <button className="px-2 py-1 text-blue-500 hover:text-blue-700 mr-2">
              <IoMdArrowBack />
            </button>
            <span className="font-medium mx-1 px-3 py-1 bg-blue-500 text-white rounded-md">1</span>
            <span className="font-medium mx-1 px-3 py-1 hover:bg-gray-200 rounded-md">2</span>
            <span className="font-medium mx-1 px-3 py-1 hover:bg-gray-200 rounded-md">3</span>
            <span className="font-medium mx-1 px-3 py-1 hover:bg-gray-200 rounded-md">4</span>
            <button className="px-2 py-1 text-blue-500 hover:text-blue-700 ml-2">
              <IoMdArrowForward />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardManagement;
