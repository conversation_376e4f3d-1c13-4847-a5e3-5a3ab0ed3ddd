import React from 'react';
import SuccessMessage from '../components/forms/SuccessMessage';

const SuccessTestPage: React.FC = () => {
  const handleClose = () => {
    console.log('Retour au dashboard');
  };

  return (
    <div className="min-h-screen bg-blue-100">
      <div className="container mx-auto py-8">
        <SuccessMessage 
          onClose={handleClose}
          title="Demande enregistrée avec succès"
          message="La demande de création de votre entreprise a bien été enregistrée. Nous attendons maintenant la validation de votre nomination par l'OMPIC, ce qui peut prendre de 24 à 48 heures."
        />
      </div>
    </div>
  );
};

export default SuccessTestPage;
