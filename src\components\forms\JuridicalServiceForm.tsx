import React, { useState } from 'react';
import { FaArrowLeft, FaUpload, FaSearch } from 'react-icons/fa';
import SuccessMessage from './SuccessMessage';

interface JuridicalServiceFormProps {
  onClose?: () => void;
}

const JuridicalServiceForm: React.FC<JuridicalServiceFormProps> = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [showSuccess, setShowSuccess] = useState(false);
  const [formData, setFormData] = useState({
    nomComplet: '',
    cin: '',
    adresse: '',
    villeCreation: '',
    typeSociete: '',
    objetsSociete: '',
    carteNationale: null as File | null,
    nom1: '',
    nom2: '',
    nom3: '',
    capitale: '',
    signature: '',
    associes: [] as Array<{
      nom: string;
      cin: string;
      adresse: string;
      type: string;
      partSociale: string;
    }>,
    newAssocie: {
      nom: '',
      cin: '',
      adresse: '',
      type: '',
      partSociale: ''
    },
    contratType: '',
    siegeSocial: '',
    searchQuery: '',
    selectedOffer: null as number | null
  });

  const totalSteps = 4;

  // Mock offers data
  const offers = [
    {
      id: 1,
      icon: '🌿',
      name: 'Nom d\'entreprise',
      address: 'Adress',
      domiciliation: '3000DH',
      creation: '3000DH'
    },
    {
      id: 2,
      icon: '🔥',
      name: 'Nom d\'entreprise',
      address: 'Adress',
      domiciliation: '2000DH',
      creation: '2000DH'
    },
    {
      id: 3,
      icon: '👤',
      name: 'Nom d\'entreprise',
      address: 'Adress',
      domiciliation: '2000DH',
      creation: '2000DH'
    }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileUpload = (file: File | null) => {
    setFormData(prev => ({
      ...prev,
      carteNationale: file
    }));
  };

  const addAssocie = () => {
    if (formData.newAssocie.nom && formData.newAssocie.cin && formData.newAssocie.adresse && formData.newAssocie.type && formData.newAssocie.partSociale) {
      setFormData(prev => ({
        ...prev,
        associes: [...prev.associes, prev.newAssocie],
        newAssocie: {
          nom: '',
          cin: '',
          adresse: '',
          type: '',
          partSociale: ''
        }
      }));
    }
  };

  const removeAssocie = (index: number) => {
    setFormData(prev => ({
      ...prev,
      associes: prev.associes.filter((_, i) => i !== index)
    }));
  };

  const handleOfferSelect = (offerId: number) => {
    setFormData(prev => ({
      ...prev,
      selectedOffer: offerId
    }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    // Handle form submission
    console.log('Form submitted:', formData);
    // Afficher le message de succès au lieu de fermer directement
    setShowSuccess(true);
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3, 4].map((step) => (
        <React.Fragment key={step}>
          <div
            className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
              step <= currentStep
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-500'
            }`}
          >
            {step}
          </div>
          {step < 4 && (
            <div
              className={`w-20 h-1 mx-3 ${
                step < currentStep ? 'bg-blue-500' : 'bg-gray-200'
              }`}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );

  const renderStep1 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
          disabled={currentStep === 1}
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-blue-500">Les informations</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-xs font-semibold text-gray-800 mb-2 uppercase tracking-wider">
            NOM COMPLET
          </label>
          <input
            type="text"
            value={formData.nomComplet}
            onChange={(e) => handleInputChange('nomComplet', e.target.value)}
            placeholder="EZZIANI Youssef"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
        <div>
          <label className="block text-xs font-semibold text-gray-800 mb-2 uppercase tracking-wider">
            CIN
          </label>
          <input
            type="text"
            value={formData.cin}
            onChange={(e) => handleInputChange('cin', e.target.value)}
            placeholder="AB123456"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ville de création
          </label>
          <input
            type="text"
            value={formData.villeCreation}
            onChange={(e) => handleInputChange('villeCreation', e.target.value)}
            placeholder="Casablanca"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Adresse
          </label>
          <input
            type="text"
            value={formData.adresse}
            onChange={(e) => handleInputChange('adresse', e.target.value)}
            placeholder="Ain Sbaa, Casablanca, Maroc"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Forme Juridique
          </label>
          <select
            value={formData.typeSociete}
            onChange={(e) => handleInputChange('typeSociete', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700 appearance-none"
          >
            <option value="">Sélectionner...</option>
            <option value="SARL">SARL</option>
            <option value="SA">SA</option>
            <option value="SNC">SNC</option>
            <option value="Auto-entrepreneur">Auto-entrepreneur</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Objets
          </label>
          <select
            value={formData.objetsSociete}
            onChange={(e) => handleInputChange('objetsSociete', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700 appearance-none"
          >
            <option value="">Sélectionner...</option>
            <option value="Commerce">Commerce</option>
            <option value="Services">Services</option>
            <option value="Industrie">Industrie</option>
            <option value="Artisanat">Artisanat</option>
            <option value="Agriculture">Agriculture</option>
          </select>
        </div>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          CIN
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center bg-gray-50">
          <input
            type="file"
            id="carte-nationale"
            className="hidden"
            accept="image/*,.pdf"
            onChange={(e) => handleFileUpload(e.target.files?.[0] || null)}
          />
          <label
            htmlFor="carte-nationale"
            className="cursor-pointer inline-flex items-center px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 font-medium shadow-sm"
          >
            <FaUpload className="mr-2" />
            Upload
          </label>
          {formData.carteNationale && (
            <p className="mt-3 text-sm text-gray-600">
              Fichier sélectionné: {formData.carteNationale.name}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-blue-500">Certificat négatif 1</h3>
      </div>

      <div className="mb-6">
        <h4 className="text-lg font-medium text-gray-800 mb-6">
          Choisissez 3 Noms pour votre entreprise
        </h4>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom 1
            </label>
            <input
              type="text"
              value={formData.nom1}
              onChange={(e) => handleInputChange('nom1', e.target.value)}
              placeholder="Nom 1"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom 2
            </label>
            <input
              type="text"
              value={formData.nom2}
              onChange={(e) => handleInputChange('nom2', e.target.value)}
              placeholder="Nom 2"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom 3
            </label>
            <input
              type="text"
              value={formData.nom3}
              onChange={(e) => handleInputChange('nom3', e.target.value)}
              placeholder="Nom 3"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
            />
          </div>
        </div>

        <div className="mt-8 p-6 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-700">
            En va déclarer le nom dans une période de 24h/48h
          </p>
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-blue-500">Associés</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Capitale
          </label>
          <input
            type="text"
            value={formData.capitale}
            onChange={(e) => handleInputChange('capitale', e.target.value)}
            placeholder="100 000 DH"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Signature
          </label>
          <select
            value={formData.signature}
            onChange={(e) => handleInputChange('signature', e.target.value)}
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-700 appearance-none"
          >
            <option value="">Sélectionner...</option>
            <option value="ET">ET</option>
            <option value="OU">OU</option>
          </select>
        </div>
      </div>

      {/* Formulaire d'ajout d'associé */}
      <div className="bg-blue-50 rounded-lg p-6 mb-6">
        <div className="flex items-center mb-6">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
            <span className="text-white text-sm">👥</span>
          </div>
          <h4 className="text-lg font-medium text-blue-600">Associés</h4>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom complet
            </label>
            <input
              type="text"
              value={formData.newAssocie?.nom || ''}
              onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, nom: e.target.value })}
              placeholder="Nom complet"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CIN
            </label>
            <input
              type="text"
              value={formData.newAssocie?.cin || ''}
              onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, cin: e.target.value })}
              placeholder="CIN"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Adresse
          </label>
          <input
            type="text"
            value={formData.newAssocie?.adresse || ''}
            onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, adresse: e.target.value })}
            placeholder="Adresse"
            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Part sociale
            </label>
            <input
              type="text"
              value={formData.newAssocie?.partSociale || ''}
              onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, partSociale: e.target.value })}
              placeholder="Part sociale"
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type associé
            </label>
            <select
              value={formData.newAssocie?.type || ''}
              onChange={(e) => handleInputChange('newAssocie', { ...formData.newAssocie, type: e.target.value })}
              className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700 appearance-none"
            >
              <option value="">Sélectionner...</option>
              <option value="Gérant">Gérant</option>
              <option value="Associé">Associé</option>
            </select>
          </div>
        </div>

        <button
          onClick={addAssocie}
          className="inline-flex items-center px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium text-sm"
        >
          <span className="mr-2">+</span>
          Ajouter à la liste
        </button>
      </div>

      {/* Table des associés */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-700 mb-4">Liste des associés</h4>
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Nom complet
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  CIN
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Adresse
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Part sociale
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-blue-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {formData.associes.map((associe, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-3 text-sm text-gray-900">{associe.nom}</td>
                  <td className="px-4 py-3 text-sm text-gray-900">{associe.cin}</td>
                  <td className="px-4 py-3 text-sm text-gray-900">{associe.adresse}</td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      associe.type === 'Gérant'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {associe.type}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">{associe.partSociale}</td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <button
                      onClick={() => removeAssocie(index)}
                      className="text-red-600 hover:text-red-800 text-sm font-medium"
                    >
                      Supprimer
                    </button>
                  </td>
                </tr>
              ))}
              {formData.associes.length === 0 && (
                <tr>
                  <td colSpan={6} className="px-4 py-8 text-center text-gray-500 text-sm">
                    Aucun associé ajouté
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handlePrevious}
          className="mr-4 p-2 text-gray-500 hover:text-gray-700"
        >
          <FaArrowLeft />
        </button>
        <h3 className="text-xl font-semibold text-blue-500">Choisissez votre contrat</h3>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Left Column - Type de contrat */}
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-3">Type de contrat</h4>
          <div className="bg-white border border-gray-200 rounded-lg p-6 h-[160px] flex flex-col">
            <div className="space-y-4 mb-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="contratType"
                  value="bail"
                  checked={formData.contratType === 'bail'}
                  onChange={(e) => handleInputChange('contratType', e.target.value)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                />
                <span className="ml-3 text-sm font-medium text-gray-900">Contrat de bail</span>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  name="contratType"
                  value="domiciliation"
                  checked={formData.contratType === 'domiciliation'}
                  onChange={(e) => handleInputChange('contratType', e.target.value)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                />
                <span className="ml-3 text-sm font-medium text-gray-900">Contrat de Domicialisation</span>
              </label>
            </div>

            <div className="mt-auto">
              <button className="inline-flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 font-medium text-sm">
                <FaUpload className="mr-2" />
                Upload
              </button>
            </div>
          </div>
        </div>

        {/* Right Column - Siège Social */}
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-3">Siège Social</h4>
          <div className="bg-white border border-gray-200 rounded-lg p-6 h-[160px] flex flex-col justify-center">
            <textarea
              value={formData.siegeSocial}
              onChange={(e) => handleInputChange('siegeSocial', e.target.value)}
              placeholder="Adresse"
              className="w-full h-[80px] px-3 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-gray-700 resize-none placeholder-gray-400"
            />
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            value={formData.searchQuery}
            onChange={(e) => handleInputChange('searchQuery', e.target.value)}
            placeholder="Trouver plus d'offres ..."
            className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 text-gray-500"
          />
          <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
            <FaSearch />
          </button>
        </div>
      </div>

      {/* Meilleures Offres */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-4">Meilleures Offres</h4>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {offers.map((offer) => (
            <div
              key={offer.id}
              onClick={() => handleOfferSelect(offer.id)}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                formData.selectedOffer === offer.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 mr-3 text-lg">
                  {offer.icon}
                </div>
                <div>
                  <p className="font-medium text-sm text-gray-900">{offer.name}</p>
                  <p className="text-xs text-gray-500">{offer.address}</p>
                </div>
              </div>

              <div className="space-y-1 mb-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">Domiciliation</span>
                  <span className="text-xs font-semibold text-gray-900">{offer.domiciliation}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600">Création</span>
                  <span className="text-xs font-semibold text-gray-900">{offer.creation}</span>
                </div>
              </div>

              <button className="w-full bg-blue-500 text-white py-2 rounded-md text-xs font-medium hover:bg-blue-600">
                Voir plus
              </button>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-start">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              className="p-2 text-gray-400 hover:text-gray-600"
              disabled={currentPage === 1}
            >
              <FaArrowLeft />
            </button>

            {[1, 2, 3, 4].map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`w-8 h-8 rounded-full text-sm font-medium ${
                  currentPage === page
                    ? 'bg-blue-500 text-white'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {page}
              </button>
            ))}

            <button
              onClick={() => setCurrentPage(Math.min(4, currentPage + 1))}
              className="p-2 text-gray-400 hover:text-gray-600"
              disabled={currentPage === 4}
            >
              <FaArrowLeft className="transform rotate-180" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      case 4:
        return renderStep4();
      default:
        return renderStep1();
    }
  };

  // Si le message de succès doit être affiché
  if (showSuccess) {
    return (
      <SuccessMessage
        onClose={onClose}
        title="Demande enregistrée avec succès"
        message="La demande de création de votre entreprise a bien été enregistrée. Nous attendons maintenant la validation de votre nomination par l'OMPIC, ce qui peut prendre de 24 à 48 heures."
      />
    );
  }

  return (
    <div className="bg-white p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Service Juridique</h2>
          <p className="text-gray-600">Création d'entreprise - Formulaire multi-étapes</p>
        </div>

        {renderStepIndicator()}
        {renderCurrentStep()}

        <div className="flex justify-between items-center mt-8">
          <button
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className={`px-6 py-3 rounded-lg font-medium ${
              currentStep === 1
                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                : 'bg-gray-500 text-white hover:bg-gray-600'
            }`}
          >
            Précédent
          </button>

          {currentStep < totalSteps ? (
            <button
              onClick={handleNext}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 font-medium"
            >
              Suivant
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 font-medium"
            >
              Terminer
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default JuridicalServiceForm;